'use client';

import { <PERSON>, <PERSON><PERSON>, Container, Flex, IconButton, Text } from '@radix-ui/themes';
import { Code, Github as GitHubIcon, Menu, X } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

export function StaticNavbar() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <Box
        asChild
        style={{
          position: 'sticky',
          top: 0,
          zIndex: 50,
          width: '100%',
          borderBottom: '1px solid var(--gray-6)',
          backgroundColor: 'var(--color-panel-translucent)',
          backdropFilter: 'blur(8px)',
        }}
      >
        <nav>
          <Container size="4">
            <Flex align="center" justify="between" height={{ initial: '56px', xs: '64px' }} px="4">
              {/* Logo */}
              <Link href="/">
                <Flex align="center" gap="2">
                  <Code size={20} className="text-[var(--accent-9)]" />
                  <Text size={{ initial: '4', xs: '5' }} weight="bold" color="gray" highContrast>
                    OnlyRules
                  </Text>
                </Flex>
              </Link>

              {/* Desktop Navigation */}
              <Box display={{ initial: 'none', sm: 'flex' }}>
                <Flex align="center" gap={{ initial: '3', md: '6' }}>
                  <Link href="/templates">
                    <Text
                      size="2"
                      weight="medium"
                      color="gray"
                      className="transition-colors hover:text-[var(--accent-9)]"
                    >
                      Browse Templates
                    </Text>
                  </Link>

                  <Link
                    href="https://github.com/ranglang/onlyrules"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Flex align="center" gap="2">
                      <GitHubIcon size={16} />
                      <Text
                        size="2"
                        color="gray"
                        className="transition-colors hover:text-[var(--accent-9)]"
                      >
                        GitHub
                      </Text>
                    </Flex>
                  </Link>

                  <Button size="2" asChild>
                    <Link href="/auth/signin">
                      <Text size="2" weight="medium">
                        Sign In
                      </Text>
                    </Link>
                  </Button>
                </Flex>
              </Box>

              {/* Mobile Menu Button */}
              <Box display={{ sm: 'none', initial: 'block' }}>
                <IconButton
                  variant="ghost"
                  size="2"
                  onClick={toggleMobileMenu}
                  aria-label="Toggle mobile menu"
                >
                  {mobileMenuOpen ? <X size={20} /> : <Menu size={20} />}
                </IconButton>
              </Box>
            </Flex>

            {/* Mobile Menu - slides down directly in navbar, no overlay */}
            {mobileMenuOpen && (
              <Box
                display={{ sm: 'none', initial: 'block' }}
                style={{
                  borderTop: '1px solid var(--gray-6)',
                  backgroundColor: 'var(--color-panel-solid)',
                }}
              >
                <Box py="4" px="4">
                  <Flex direction="column" gap="4">
                    <Link href="/templates" onClick={() => setMobileMenuOpen(false)}>
                      <Text
                        size="2"
                        weight="medium"
                        className="transition-colors hover:text-[var(--accent-9)]"
                      >
                        Browse Templates
                      </Text>
                    </Link>

                    <Link
                      href="https://github.com/ranglang/onlyrules"
                      target="_blank"
                      rel="noopener noreferrer"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <Flex align="center" gap="2">
                        <GitHubIcon size={16} />
                        <Text size="2" className="transition-colors hover:text-[var(--accent-9)]">
                          GitHub
                        </Text>
                      </Flex>
                    </Link>

                    <Button size="3" style={{ width: '100%' }} asChild>
                      <Link href="/auth/signin" onClick={() => setMobileMenuOpen(false)}>
                        <Text size="2" weight="medium">
                          Sign In
                        </Text>
                      </Link>
                    </Button>
                  </Flex>
                </Box>
              </Box>
            )}
          </Container>
        </nav>
      </Box>
  );
}
