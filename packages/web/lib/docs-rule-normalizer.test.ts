/**
 * Tests for docs-rule-normalizer.ts
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { normalizeMDCToRule, getAllDocsRules } from './docs-rule-normalizer';
import type { Rule } from './store';

// Mock file system operations
vi.mock('node:fs', () => ({
  readFileSync: vi.fn(),
  readdirSync: vi.fn(),
}));

vi.mock('node:path', () => ({
  join: vi.fn((...args) => args.join('/')),
}));

const mockReadFileSync = vi.mocked(await import('node:fs')).readFileSync;
const mockReaddirSync = vi.mocked(await import('node:fs')).readdirSync;

describe('normalizeMDCToRule', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock Date.now() for consistent IDs
    vi.spyOn(Date, 'now').mockReturnValue(1234567890);
    vi.spyOn(Date.prototype, 'toISOString').mockReturnValue('2023-01-01T00:00:00.000Z');
  });

  it('should normalize a complete MDC file with frontmatter', () => {
    const mdcContent = `---
title: "Frontend Development Rules"
description: "How to write nextjs app"
author: "Windsurf AI"
tags: ["Nextjs", "React"]
---

# General Code Style & Formatting
- Follow the Airbnb Style Guide for code formatting.
- Use PascalCase for React component file names.

# Project Structure & Architecture
- Follow Next.js patterns and use the App Router.`;

    const result = normalizeMDCToRule('nextjs.mdc', mdcContent);

    expect(result).toEqual({
      id: 'docs_nextjs_1234567890',
      title: 'Frontend Development Rules',
      description: 'How to write nextjs app',
      content: `# General Code Style & Formatting
- Follow the Airbnb Style Guide for code formatting.
- Use PascalCase for React component file names.

# Project Structure & Architecture
- Follow Next.js patterns and use the App Router.`,
      visibility: 'PUBLIC',
      applyType: 'manual',
      glob: undefined,
      shareToken: null,
      createdAt: '2023-01-01T00:00:00.000Z',
      updatedAt: '2023-01-01T00:00:00.000Z',
      userId: 'system',
      tags: [
        {
          tag: {
            id: 'tag_nextjs',
            name: 'Nextjs',
            color: '#3B82F6'
          }
        },
        {
          tag: {
            id: 'tag_react',
            name: 'React',
            color: '#3B82F6'
          }
        }
      ],
      user: {
        id: 'system',
        name: 'Windsurf AI',
        email: '<EMAIL>',
        image: null,
        avatar: null
      }
    });
  });

  it('should handle MDC file without frontmatter', () => {
    const mdcContent = `# TypeScript Best Practices

Always use strict types and avoid any.
Use proper naming conventions.`;

    const result = normalizeMDCToRule('typescript.mdc', mdcContent);

    expect(result.title).toBe('typescript');
    expect(result.description).toBeNull();
    expect(result.content).toBe(mdcContent);
    expect(result.tags).toEqual([]);
    expect(result.user?.name).toBe('System');
  });

  it('should handle empty or missing tags', () => {
    const mdcContent = `---
title: "Test Rule"
description: "Test description"
author: "Test Author"
tags: []
---

Content here`;

    const result = normalizeMDCToRule('test.mdc', mdcContent);

    expect(result.tags).toEqual([]);
  });

  it('should handle missing optional frontmatter fields', () => {
    const mdcContent = `---
title: "Minimal Rule"
---

Just content`;

    const result = normalizeMDCToRule('minimal.mdc', mdcContent);

    expect(result.title).toBe('Minimal Rule');
    expect(result.description).toBeNull();
    expect(result.user?.name).toBe('System');
    expect(result.tags).toEqual([]);
  });

  it('should generate proper tag IDs from tag names', () => {
    const mdcContent = `---
title: "Tag Test"
tags: ["Next.js", "Type Script", "Node JS"]
---

Content`;

    const result = normalizeMDCToRule('tagtest.mdc', mdcContent);

    expect(result.tags).toEqual([
      {
        tag: {
          id: 'tag_next.js',
          name: 'Next.js',
          color: '#3B82F6'
        }
      },
      {
        tag: {
          id: 'tag_type_script',
          name: 'Type Script',
          color: '#3B82F6'
        }
      },
      {
        tag: {
          id: 'tag_node_js',
          name: 'Node JS',
          color: '#3B82F6'
        }
      }
    ]);
  });

  it('should handle quoted values in frontmatter', () => {
    const mdcContent = `---
title: "Quoted Title"
description: 'Single quoted description'
author: "Author Name"
---

Content`;

    const result = normalizeMDCToRule('quoted.mdc', mdcContent);

    expect(result.title).toBe('Quoted Title');
    expect(result.description).toBe('Single quoted description');
    expect(result.user?.name).toBe('Author Name');
  });
});

describe('getAllDocsRules', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.spyOn(Date, 'now').mockReturnValue(1234567890);
    vi.spyOn(Date.prototype, 'toISOString').mockReturnValue('2023-01-01T00:00:00.000Z');
  });

  it('should read and normalize all MDC files from docs/rules directory', () => {
    const mockFiles = ['nextjs.mdc', 'typescript.mdc', 'readme.txt'];
    const nextjsContent = `---
title: "Next.js Rules"
tags: ["Nextjs"]
---
Next.js content`;

    const typescriptContent = `---
title: "TypeScript Rules"
tags: ["TypeScript"]
---
TypeScript content`;

    mockReaddirSync.mockReturnValue(mockFiles as any);
    mockReadFileSync
      .mockReturnValueOnce(nextjsContent)
      .mockReturnValueOnce(typescriptContent);

    const result = getAllDocsRules();

    expect(mockReaddirSync).toHaveBeenCalledWith('/mnt/persist/workspace/packages/web/../../packages/docs/rules');
    expect(mockReadFileSync).toHaveBeenCalledTimes(2);
    expect(result).toHaveLength(2);
    expect(result[0].title).toBe('Next.js Rules');
    expect(result[1].title).toBe('TypeScript Rules');
  });

  it('should handle file system errors gracefully', () => {
    mockReaddirSync.mockImplementation(() => {
      throw new Error('Directory not found');
    });

    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    const result = getAllDocsRules();

    expect(result).toEqual([]);
    expect(consoleSpy).toHaveBeenCalledWith('Error reading docs rules:', expect.any(Error));
    
    consoleSpy.mockRestore();
  });

  it('should filter out non-MDC files', () => {
    const mockFiles = ['nextjs.mdc', 'readme.md', 'config.json', 'typescript.mdc'];
    
    mockReaddirSync.mockReturnValue(mockFiles as any);
    mockReadFileSync.mockReturnValue('---\ntitle: Test\n---\nContent');

    const result = getAllDocsRules();

    expect(mockReadFileSync).toHaveBeenCalledTimes(2); // Only .mdc files
    expect(result).toHaveLength(2);
  });
});
