import { Button, Container, Flex, Heading, Text } from '@radix-ui/themes';
import Link from 'next/link';

export default function NotFound() {
  return (
    <Container size="2" className="flex min-h-screen items-center justify-center">
      <Flex direction="column" align="center" gap="6" className="text-center">
        <Heading size="9" className="font-bold text-6xl">
          404
        </Heading>
        <Heading size="6">Page Not Found</Heading>
        <Text size="4" color="gray" className="max-w-md">
          Sorry, we couldn't find the page you're looking for. The page might have been moved,
          deleted, or you might have entered the wrong URL.
        </Text>
        <Flex gap="3" className="mt-4">
          <Button variant="solid" asChild>
            <Link href="/">
              Go Home
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/rules">
              Browse Rules
            </Link>
          </Button>
        </Flex>
      </Flex>
    </Container>
  );
}
